"""
AI-powered job search service with intelligent matching and recommendations
"""
import asyncio
import logging
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta

from app.models.job import JobSearchCriteria, JobPost
from app.models.resume import ParsedResumeContent
from app.core.config import settings
from app.db.job_repository import job_repository
from app.services.ai_service import ai_service
from app.services.embedding_service import embedding_service
from app.services.job_matching_service import job_matching_service

logger = logging.getLogger(__name__)


class JobSearchService:
    """Service for searching and managing job postings"""
    
    def __init__(self):
        self.supported_sites = ["linkedin", "indeed", "glassdoor", "zip_recruiter"]
        self.max_results_per_site = 50
    
    async def search_jobs(self, search_criteria: JobSearchCriteria) -> List[JobPost]:
        """
        Search for jobs using JobSpy across multiple platforms
        
        Args:
            search_criteria: Search parameters
            
        Returns:
            List of job postings
        """
        try:
            # Import JobSpy here to avoid import errors if not installed
            try:
                from jobspy import scrape_jobs
            except ImportError:
                logger.warning("JobSpy not installed, using fallback job search")
                return await self._fallback_job_search(search_criteria)
            
            all_jobs = []
            
            # Search each supported site
            for site in self.supported_sites:
                try:
                    logger.info(f"Searching {site} for jobs...")
                    
                    # Prepare search parameters
                    search_params = {
                        "site_name": [site],
                        "search_term": search_criteria.keywords,
                        "location": search_criteria.location,
                        "results_wanted": min(search_criteria.limit or 20, self.max_results_per_site),
                        "hours_old": 72,  # Jobs posted in last 3 days
                        "country_indeed": search_criteria.country or "USA"
                    }
                    
                    # Add job type filter if specified
                    if search_criteria.job_type:
                        search_params["job_type"] = search_criteria.job_type
                    
                    # Add experience level filter if specified
                    if search_criteria.experience_level:
                        search_params["experience_level"] = search_criteria.experience_level
                    
                    # Perform the search
                    jobs_df = scrape_jobs(**search_params)
                    
                    if jobs_df is not None and not jobs_df.empty:
                        # Convert DataFrame to JobPost objects
                        site_jobs = await self._convert_dataframe_to_jobs(jobs_df, site)
                        all_jobs.extend(site_jobs)
                        
                        logger.info(f"Found {len(site_jobs)} jobs from {site}")
                    else:
                        logger.warning(f"No jobs found on {site}")
                        
                except Exception as e:
                    logger.error(f"Error searching {site}: {e}")
                    continue
            
            # Remove duplicates based on job URL or title+company
            unique_jobs = await self._deduplicate_jobs(all_jobs)
            
            # Sort by relevance/date
            sorted_jobs = sorted(unique_jobs, key=lambda x: x.date_posted or datetime.min, reverse=True)
            
            # Apply final limit
            final_limit = search_criteria.limit or 100
            result_jobs = sorted_jobs[:final_limit]
            
            # Save jobs to database for future reference
            await self._save_jobs_to_database(result_jobs)
            
            logger.info(f"Job search completed: {len(result_jobs)} unique jobs found")
            return result_jobs
            
        except Exception as e:
            logger.error(f"Job search failed: {e}")
            # Return fallback results
            return await self._fallback_job_search(search_criteria)
    
    async def get_saved_jobs(self, user_id: str, limit: int = 50) -> List[JobPost]:
        """
        Get jobs that have been scraped and saved for the user
        
        Args:
            user_id: User ID
            limit: Maximum number of jobs to return
            
        Returns:
            List of saved job postings
        """
        try:
            jobs = await job_repository.get_recent_jobs(limit=limit)
            return jobs
        except Exception as e:
            logger.error(f"Failed to get saved jobs: {e}")
            return []
    
    async def _convert_dataframe_to_jobs(self, jobs_df, source_site: str) -> List[JobPost]:
        """Convert pandas DataFrame to JobPost objects"""
        jobs = []
        
        for _, row in jobs_df.iterrows():
            try:
                # Extract job data with fallbacks
                job_data = {
                    "id": f"{source_site}_{hash(str(row.get('job_url', '')) + str(row.get('title', '')))}",
                    "title": str(row.get("title", "")).strip(),
                    "company": str(row.get("company", "")).strip(),
                    "location": str(row.get("location", "")).strip(),
                    "job_url": str(row.get("job_url", "")).strip(),
                    "job_type": str(row.get("job_type", "")).strip(),
                    "date_posted": self._parse_date(row.get("date_posted")),
                    "salary_min": self._parse_salary(row.get("min_amount")),
                    "salary_max": self._parse_salary(row.get("max_amount")),
                    "currency": str(row.get("currency", "USD")).strip(),
                    "description": str(row.get("description", "")).strip(),
                    "source": source_site,
                    "is_remote": "remote" in str(row.get("location", "")).lower(),
                    "experience_level": str(row.get("experience_level", "")).strip(),
                    "scraped_at": datetime.now()
                }
                
                # Only add jobs with minimum required fields
                if job_data["title"] and job_data["company"]:
                    job = JobPost(**job_data)
                    jobs.append(job)
                    
            except Exception as e:
                logger.warning(f"Failed to parse job row: {e}")
                continue
        
        return jobs
    
    async def _deduplicate_jobs(self, jobs: List[JobPost]) -> List[JobPost]:
        """Remove duplicate jobs based on URL or title+company combination"""
        seen_urls = set()
        seen_combinations = set()
        unique_jobs = []
        
        for job in jobs:
            # Check URL first
            if job.job_url and job.job_url in seen_urls:
                continue
            
            # Check title+company combination
            combination = f"{job.title.lower()}_{job.company.lower()}"
            if combination in seen_combinations:
                continue
            
            # Add to unique list
            unique_jobs.append(job)
            if job.job_url:
                seen_urls.add(job.job_url)
            seen_combinations.add(combination)
        
        return unique_jobs
    
    async def _save_jobs_to_database(self, jobs: List[JobPost]):
        """Save jobs to database for future reference"""
        try:
            for job in jobs:
                await job_repository.create_or_update_job(job)
        except Exception as e:
            logger.error(f"Failed to save jobs to database: {e}")
    
    async def _fallback_job_search(self, search_criteria: JobSearchCriteria) -> List[JobPost]:
        """Fallback job search when JobSpy is not available"""
        logger.info("Using fallback job search (mock data)")
        
        # Return mock job data for development/testing
        mock_jobs = [
            JobPost(
                id="mock_1",
                title=f"Software Engineer - {search_criteria.keywords}",
                company="Tech Corp",
                location=search_criteria.location or "Remote",
                job_url="https://example.com/job1",
                job_type="full_time",
                date_posted=datetime.now() - timedelta(days=1),
                salary_min=80000,
                salary_max=120000,
                currency="USD",
                description=f"Exciting opportunity for {search_criteria.keywords} development...",
                source="fallback",
                is_remote=True,
                scraped_at=datetime.now()
            ),
            JobPost(
                id="mock_2",
                title=f"Senior {search_criteria.keywords} Developer",
                company="Innovation Inc",
                location=search_criteria.location or "New York, NY",
                job_url="https://example.com/job2",
                job_type="full_time",
                date_posted=datetime.now() - timedelta(days=2),
                salary_min=100000,
                salary_max=150000,
                currency="USD",
                description=f"Senior role in {search_criteria.keywords} with great benefits...",
                source="fallback",
                is_remote=False,
                scraped_at=datetime.now()
            )
        ]
        
        return mock_jobs[:search_criteria.limit or 10]
    
    def _parse_date(self, date_value) -> Optional[datetime]:
        """Parse date from various formats"""
        if not date_value:
            return None
        
        try:
            if isinstance(date_value, datetime):
                return date_value
            elif isinstance(date_value, str):
                # Try common date formats
                for fmt in ["%Y-%m-%d", "%m/%d/%Y", "%d/%m/%Y"]:
                    try:
                        return datetime.strptime(date_value, fmt)
                    except ValueError:
                        continue
        except Exception:
            pass
        
        return None
    
    def _parse_salary(self, salary_value) -> Optional[int]:
        """Parse salary from various formats"""
        if not salary_value:
            return None
        
        try:
            if isinstance(salary_value, (int, float)):
                return int(salary_value)
            elif isinstance(salary_value, str):
                # Remove common salary formatting
                cleaned = salary_value.replace("$", "").replace(",", "").replace("K", "000")
                return int(float(cleaned))
        except Exception:
            pass
        
        return None


# Global instance
job_search_service = JobSearchService()
