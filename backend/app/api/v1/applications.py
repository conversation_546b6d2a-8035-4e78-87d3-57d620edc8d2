"""
Job application management API endpoints
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, status
from fastapi.security import HTTPBearer

from app.models.user import UserInDB
from app.models.application import (
    Application, ApplicationCreate, ApplicationUpdate, 
    ApplicationStatus, ApplicationResponse
)
from app.api.v1.auth import get_current_user
from app.services.application_orchestrator import application_orchestrator
from app.services.automated_workflow_service import automated_workflow_service
from app.db.application_repository import application_repository

router = APIRouter()
security = HTTPBearer()


@router.post("/", response_model=ApplicationResponse)
async def create_application(
    application_data: ApplicationCreate,
    current_user: UserInDB = Depends(get_current_user)
):
    """
    Create and submit a job application
    
    - **application_data**: Application details
    - Returns application result
    """
    try:
        result = await application_orchestrator.orchestrate_single_application(
            job_id=application_data.job_id,
            user_id=current_user.id,
            resume_id=application_data.resume_id,
            force_apply=application_data.force_apply or False
        )
        
        return ApplicationResponse(
            id=result.application_id,
            status=result.status.value,
            message=result.message,
            application_url=result.application_url
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create application: {str(e)}"
        )


@router.get("/", response_model=List[Application])
async def get_user_applications(
    current_user: UserInDB = Depends(get_current_user),
    status_filter: Optional[ApplicationStatus] = Query(None),
    limit: int = Query(50, ge=1, le=200),
    offset: int = Query(0, ge=0)
):
    """
    Get user's job applications with optional filtering
    
    - **status_filter**: Filter by application status
    - **limit**: Maximum number of applications to return
    - **offset**: Number of applications to skip
    - Returns list of applications
    """
    try:
        applications = await application_repository.get_user_applications(
            user_id=current_user.id,
            status_filter=status_filter,
            limit=limit,
            offset=offset
        )
        return applications
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get applications: {str(e)}"
        )


@router.get("/{application_id}", response_model=Application)
async def get_application(
    application_id: str,
    current_user: UserInDB = Depends(get_current_user)
):
    """
    Get a specific application by ID
    
    - **application_id**: Application ID
    - Returns application details
    """
    try:
        application = await application_repository.get_application_by_id(application_id)
        
        if not application:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Application not found"
            )
        
        # Check if user owns this application
        if application.user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        return application
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get application: {str(e)}"
        )


@router.put("/{application_id}", response_model=Application)
async def update_application(
    application_id: str,
    application_update: ApplicationUpdate,
    current_user: UserInDB = Depends(get_current_user)
):
    """
    Update an application's status or details
    
    - **application_id**: Application ID
    - **application_update**: Updated application data
    - Returns updated application
    """
    try:
        application = await application_repository.get_application_by_id(application_id)
        
        if not application:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Application not found"
            )
        
        # Check if user owns this application
        if application.user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        updated_application = await application_repository.update_application(
            application_id=application_id,
            update_data=application_update
        )
        
        return updated_application
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update application: {str(e)}"
        )


@router.delete("/{application_id}")
async def delete_application(
    application_id: str,
    current_user: UserInDB = Depends(get_current_user)
):
    """
    Delete an application
    
    - **application_id**: Application ID
    - Returns success message
    """
    try:
        application = await application_repository.get_application_by_id(application_id)
        
        if not application:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Application not found"
            )
        
        # Check if user owns this application
        if application.user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        await application_repository.delete_application(application_id)
        
        return {"message": "Application deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete application: {str(e)}"
        )


@router.post("/automation/start")
async def start_automation(
    current_user: UserInDB = Depends(get_current_user)
):
    """
    Start automated job application workflow for the user
    
    - Returns automation status
    """
    try:
        result = await automated_workflow_service.enable_automation_for_user(
            user_id=current_user.id,
            schedule_next_run=True
        )
        
        return {
            "message": "Automation started successfully",
            "status": result.get("status"),
            "next_run": result.get("next_run")
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start automation: {str(e)}"
        )


@router.post("/automation/stop")
async def stop_automation(
    current_user: UserInDB = Depends(get_current_user)
):
    """
    Stop automated job application workflow for the user
    
    - Returns automation status
    """
    try:
        result = await automated_workflow_service.disable_automation_for_user(
            user_id=current_user.id
        )
        
        return {
            "message": "Automation stopped successfully",
            "status": result.get("status")
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to stop automation: {str(e)}"
        )


@router.get("/automation/status")
async def get_automation_status(
    current_user: UserInDB = Depends(get_current_user)
):
    """
    Get current automation status for the user
    
    - Returns automation status and statistics
    """
    try:
        status_info = await automated_workflow_service.get_user_automation_status(
            user_id=current_user.id
        )
        
        return status_info
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get automation status: {str(e)}"
        )
