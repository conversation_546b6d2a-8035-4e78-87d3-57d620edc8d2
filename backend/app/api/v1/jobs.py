"""
Job search and management API endpoints
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, status
from fastapi.security import HTT<PERSON><PERSON>earer

from app.models.user import UserInDB
from app.models.job import JobSearchCriteria, JobPost, JobMatchResult
from app.api.v1.auth import get_current_user
from app.services.job_search_service import job_search_service
from app.services.job_matching_service import job_matching_service
from app.db.resume_repository import resume_repository
from app.db.preferences_repository import preferences_repository

router = APIRouter()
security = HTTPBearer()


@router.post("/search", response_model=List[JobPost])
async def search_jobs(
    search_criteria: JobSearchCriteria,
    current_user: UserInDB = Depends(get_current_user)
):
    """
    Search for jobs based on criteria
    
    - **search_criteria**: Job search parameters
    - Returns list of job postings
    """
    try:
        jobs = await job_search_service.search_jobs(search_criteria)
        return jobs
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Job search failed: {str(e)}"
        )


@router.get("/recommendations", response_model=List[JobMatchResult])
async def get_job_recommendations(
    current_user: UserInDB = Depends(get_current_user),
    limit: int = Query(20, ge=1, le=100),
    min_score: float = Query(0.7, ge=0.0, le=1.0)
):
    """
    Get personalized job recommendations based on user's resume and preferences
    
    - **limit**: Maximum number of recommendations
    - **min_score**: Minimum match score threshold
    - Returns list of job matches with scores
    """
    try:
        # Get user's latest resume
        resumes = await resume_repository.get_user_resumes(current_user.id)
        if not resumes:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No resume found. Please upload a resume first."
            )
        
        latest_resume = resumes[0]  # Assuming first is latest
        
        # Get user preferences
        preferences = await preferences_repository.get_preferences(current_user.id)
        if not preferences:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No preferences found. Please set your job preferences first."
            )
        
        # Get job recommendations
        recommendations = await job_matching_service.get_job_recommendations(
            user_id=current_user.id,
            resume_data=latest_resume,
            user_preferences=preferences,
            limit=limit,
            min_score=min_score
        )
        
        return recommendations
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get recommendations: {str(e)}"
        )


@router.get("/{job_id}/match", response_model=JobMatchResult)
async def calculate_job_match(
    job_id: str,
    current_user: UserInDB = Depends(get_current_user)
):
    """
    Calculate match score between a specific job and user's profile
    
    - **job_id**: Job ID to analyze
    - Returns match analysis
    """
    try:
        # Get user's latest resume
        resumes = await resume_repository.get_user_resumes(current_user.id)
        if not resumes:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No resume found. Please upload a resume first."
            )
        
        latest_resume = resumes[0]
        
        # Get user preferences
        preferences = await preferences_repository.get_preferences(current_user.id)
        if not preferences:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No preferences found. Please set your job preferences first."
            )
        
        # Get job details and calculate match
        match_result = await job_matching_service.calculate_single_job_match(
            job_id=job_id,
            resume_data=latest_resume,
            user_preferences=preferences
        )
        
        return match_result
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to calculate job match: {str(e)}"
        )


@router.get("/", response_model=List[JobPost])
async def get_saved_jobs(
    current_user: UserInDB = Depends(get_current_user),
    limit: int = Query(50, ge=1, le=200)
):
    """
    Get jobs that have been scraped and saved for the user
    
    - **limit**: Maximum number of jobs to return
    - Returns list of saved job postings
    """
    try:
        jobs = await job_search_service.get_saved_jobs(
            user_id=current_user.id,
            limit=limit
        )
        return jobs
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get saved jobs: {str(e)}"
        )
