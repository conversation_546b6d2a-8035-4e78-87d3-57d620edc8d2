"""
Application-related Pydantic models
"""
from datetime import datetime
from typing import Optional, Dict, Any
from pydantic import BaseModel, Field
from enum import Enum


class ApplicationStatus(str, Enum):
    """Application status enumeration"""
    PENDING = "PENDING"
    SUBMITTED = "SUBMITTED"
    VIEWED = "VIEWED"
    REJECTED = "REJECTED"
    INTERVIEW_SCHEDULED = "INTERVIEW_SCHEDULED"
    OFFER_RECEIVED = "OFFER_RECEIVED"
    ACCEPTED = "ACCEPTED"
    DECLINED = "DECLINED"


class ApplicationBase(BaseModel):
    """Base application model"""
    job_post_id: str
    resume_id: str
    status: ApplicationStatus = ApplicationStatus.PENDING


class ApplicationCreate(ApplicationBase):
    """Model for creating a new application"""
    job_id: str = Field(..., description="Job ID to apply to")
    resume_id: str = Field(..., description="Resume ID to use for application")
    force_apply: Optional[bool] = Field(False, description="Force application even if already applied")


class ApplicationUpdate(BaseModel):
    """Model for updating an application"""
    status: Optional[ApplicationStatus] = None
    application_url: Optional[str] = None
    notes: Optional[str] = None


class Application(ApplicationBase):
    """Full application model"""
    id: str
    user_id: str
    match_score: float
    customized_resume_content: str
    cover_letter_content: str
    application_url: Optional[str] = None
    applied_at: datetime
    last_status_update: datetime
    
    class Config:
        from_attributes = True


class ApplicationResponse(BaseModel):
    """Response model for application operations"""
    id: str
    status: str
    message: str
    application_url: Optional[str] = None


class ApplicationStats(BaseModel):
    """Application statistics model"""
    total_applications: int = 0
    pending_applications: int = 0
    submitted_applications: int = 0
    interview_applications: int = 0
    offer_applications: int = 0
    rejected_applications: int = 0
    response_rate: float = 0.0
    average_match_score: float = 0.0


class AutomationSettings(BaseModel):
    """Automation settings for job applications"""
    enabled: bool = False
    auto_apply_threshold: float = Field(0.85, ge=0.0, le=1.0)
    max_applications_per_day: int = Field(10, ge=1, le=50)
    max_applications_per_week: int = Field(25, ge=1, le=100)
    require_manual_approval: bool = False
    application_delay_minutes: int = Field(30, ge=5, le=240)
    excluded_companies: list[str] = Field(default_factory=list)
    preferred_job_types: list[str] = Field(default_factory=list)


class AutomationStatus(BaseModel):
    """Current automation status"""
    is_active: bool = False
    last_run: Optional[datetime] = None
    next_run: Optional[datetime] = None
    daily_applications: int = 0
    weekly_applications: int = 0
    total_applications: int = 0
    current_execution_status: Optional[str] = None
    error_message: Optional[str] = None
