# AI Job Agent Environment Configuration
# Copy this file to .env and update the values

# Security
SECRET_KEY=your-secret-key-change-in-production-make-it-long-and-random
ACCESS_TOKEN_EXPIRE_MINUTES=11520

# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/ai_job_agent

# Redis Configuration
REDIS_URL=redis://localhost:6379

# AI Services
GEMINI_API_KEY=your-gemini-api-key-here
OPENAI_API_KEY=your-openai-api-key-here

# Vector Database (Pinecone)
PINECONE_API_KEY=your-pinecone-api-key-here
PINECONE_ENVIRONMENT=your-pinecone-environment
PINECONE_INDEX_NAME=ai-job-agent

# CORS Origins (comma-separated)
BACKEND_CORS_ORIGINS=http://localhost:3000,http://localhost:8501

# Logging Configuration
LOG_LEVEL=INFO
LOG_TO_DATABASE=true
LOG_RETENTION_DAYS=90

# File Upload Settings
MAX_FILE_SIZE=10485760
UPLOAD_DIR=uploads

# Job Search Settings
MAX_JOBS_PER_SEARCH=100
APPLICATION_RATE_LIMIT=10
