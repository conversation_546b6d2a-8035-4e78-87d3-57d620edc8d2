#!/usr/bin/env python3
"""
AI Job Agent Feature Status Checker
Comprehensive check of all implemented features and their sync status
"""
import asyncio
import os
import sys
from pathlib import Path

# Add backend to path
backend_path = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_path))

from app.core.config import settings
from app.services.ai_service import ai_service
from app.services.redis_service import redis_service
from app.db.database import init_database, close_database


class FeatureChecker:
    """Comprehensive feature status checker"""
    
    def __init__(self):
        self.results = {}
        self.errors = []
    
    async def check_all_features(self):
        """Run all feature checks"""
        print("🤖 AI Job Agent - Feature Status Check")
        print("=" * 50)
        
        # Core Infrastructure
        await self.check_database()
        await self.check_redis()
        await self.check_ai_services()
        
        # API Endpoints
        self.check_api_endpoints()
        
        # Frontend Components
        self.check_frontend_components()
        
        # AI Workflow Components
        await self.check_ai_workflow()
        
        # Configuration
        self.check_configuration()
        
        # Dependencies
        self.check_dependencies()
        
        # Generate Report
        self.generate_report()
    
    async def check_database(self):
        """Check database connectivity and schema"""
        print("\n📊 Database Status:")
        try:
            await init_database()
            self.results["database"] = {
                "status": "✅ Connected",
                "url": settings.DATABASE_URL.split("@")[-1] if "@" in settings.DATABASE_URL else "localhost",
                "schema": "✅ Prisma schema exists"
            }
            print("  ✅ Database connection successful")
            await close_database()
        except Exception as e:
            self.results["database"] = {"status": "❌ Failed", "error": str(e)}
            self.errors.append(f"Database: {e}")
            print(f"  ❌ Database connection failed: {e}")
    
    async def check_redis(self):
        """Check Redis connectivity"""
        print("\n🔴 Redis Status:")
        try:
            await redis_service.connect()
            if redis_service.redis_client:
                self.results["redis"] = {"status": "✅ Connected", "url": settings.REDIS_URL}
                print("  ✅ Redis connection successful")
            else:
                raise Exception("Redis client not initialized")
            await redis_service.disconnect()
        except Exception as e:
            self.results["redis"] = {"status": "❌ Failed", "error": str(e)}
            self.errors.append(f"Redis: {e}")
            print(f"  ❌ Redis connection failed: {e}")
    
    async def check_ai_services(self):
        """Check AI service initialization"""
        print("\n🧠 AI Services Status:")
        
        # Check Gemini API Key
        if settings.GEMINI_API_KEY:
            try:
                await ai_service.initialize()
                self.results["gemini"] = {"status": "✅ Initialized", "model": "gemini-2.5-flash"}
                print("  ✅ Gemini AI service initialized")
            except Exception as e:
                self.results["gemini"] = {"status": "❌ Failed", "error": str(e)}
                self.errors.append(f"Gemini: {e}")
                print(f"  ❌ Gemini initialization failed: {e}")
        else:
            self.results["gemini"] = {"status": "⚠️ No API Key", "note": "Will use fallback methods"}
            print("  ⚠️ Gemini API key not configured - using fallback methods")
        
        # Check Pinecone
        if settings.PINECONE_API_KEY:
            self.results["pinecone"] = {"status": "✅ Configured", "index": settings.PINECONE_INDEX_NAME}
            print("  ✅ Pinecone vector database configured")
        else:
            self.results["pinecone"] = {"status": "⚠️ No API Key", "note": "Vector search disabled"}
            print("  ⚠️ Pinecone API key not configured - vector search disabled")
    
    def check_api_endpoints(self):
        """Check API endpoint implementation"""
        print("\n🌐 API Endpoints Status:")
        
        endpoints = {
            "auth": ["auth/login", "auth/register", "auth/refresh"],
            "users": ["users/profile"],
            "resume": ["resume/upload", "resume/analyze", "resume/"],
            "preferences": ["preferences/"],
            "jobs": ["jobs/search", "jobs/recommendations", "jobs/{id}/match"],
            "applications": ["applications/", "applications/automation/start"]
        }

        for category, endpoint_list in endpoints.items():
            implemented = self.check_files_exist([
                f"backend/app/api/v1/{category}.py"
            ])
            
            if implemented:
                self.results[f"api_{category.lower()}"] = {
                    "status": "✅ Implemented",
                    "endpoints": len(endpoint_list)
                }
                print(f"  ✅ {category} API endpoints implemented ({len(endpoint_list)} endpoints)")
            else:
                self.results[f"api_{category.lower()}"] = {"status": "❌ Missing"}
                print(f"  ❌ {category} API endpoints missing")
    
    def check_frontend_components(self):
        """Check frontend component implementation"""
        print("\n🖥️ Frontend Components Status:")
        
        components = {
            "Main App": "frontend/streamlit_app.py",
            "Resume Upload": "frontend/pages/resume_upload.py",
            "Dashboard": "frontend/pages/dashboard.py",
            "Job Review": "frontend/pages/job_review.py",
            "Preferences": "frontend/pages/preferences.py",
            "Application Tracking": "frontend/pages/application_tracking.py",
            "API Client": "frontend/app/utils/api_client.py",
            "Authentication": "frontend/app/utils/streamlit_auth.py"
        }
        
        for name, path in components.items():
            if os.path.exists(path):
                self.results[f"frontend_{name.lower().replace(' ', '_')}"] = {"status": "✅ Implemented"}
                print(f"  ✅ {name} component implemented")
            else:
                self.results[f"frontend_{name.lower().replace(' ', '_')}"] = {"status": "❌ Missing"}
                print(f"  ❌ {name} component missing")
    
    async def check_ai_workflow(self):
        """Check AI workflow components"""
        print("\n🔄 AI Workflow Status:")
        
        workflow_components = {
            "Resume Parsing": "backend/app/services/resume_service.py",
            "Job Matching": "backend/app/services/job_matching_service.py",
            "Application Orchestrator": "backend/app/services/application_orchestrator.py",
            "Automated Workflow": "backend/app/services/automated_workflow_service.py",
            "LangGraph Workflows": "backend/app/services/langgraph_workflows.py",
            "Embedding Service": "backend/app/services/embedding_service.py"
        }
        
        for name, path in workflow_components.items():
            if os.path.exists(path):
                self.results[f"workflow_{name.lower().replace(' ', '_')}"] = {"status": "✅ Implemented"}
                print(f"  ✅ {name} implemented")
            else:
                self.results[f"workflow_{name.lower().replace(' ', '_')}"] = {"status": "❌ Missing"}
                print(f"  ❌ {name} missing")
    
    def check_configuration(self):
        """Check configuration status"""
        print("\n⚙️ Configuration Status:")
        
        config_items = {
            "Environment File": ".env",
            "Example Environment": ".env.example",
            "Docker Compose": "docker-compose.yml",
            "Prisma Schema": "backend/prisma/schema.prisma",
            "Requirements": "requirements.txt",
            "Makefile": "Makefile"
        }
        
        for name, path in config_items.items():
            if os.path.exists(path):
                self.results[f"config_{name.lower().replace(' ', '_')}"] = {"status": "✅ Present"}
                print(f"  ✅ {name} present")
            else:
                status = "⚠️ Missing" if name == "Environment File" else "❌ Missing"
                self.results[f"config_{name.lower().replace(' ', '_')}"] = {"status": status}
                print(f"  {status} {name}")
    
    def check_dependencies(self):
        """Check if all dependencies are properly specified"""
        print("\n📦 Dependencies Status:")
        
        try:
            with open("requirements.txt", "r") as f:
                requirements = f.read()
            
            critical_deps = [
                "fastapi", "uvicorn", "streamlit", "prisma", "redis",
                "langchain", "langchain-google-genai", "pinecone-client",
                "python-docx", "PyPDF2", "pydantic"
            ]
            
            missing_deps = []
            for dep in critical_deps:
                if dep not in requirements:
                    missing_deps.append(dep)
            
            if not missing_deps:
                self.results["dependencies"] = {"status": "✅ All Critical Dependencies Present"}
                print("  ✅ All critical dependencies present in requirements.txt")
            else:
                self.results["dependencies"] = {
                    "status": "⚠️ Missing Dependencies",
                    "missing": missing_deps
                }
                print(f"  ⚠️ Missing dependencies: {', '.join(missing_deps)}")
                
        except FileNotFoundError:
            self.results["dependencies"] = {"status": "❌ requirements.txt not found"}
            print("  ❌ requirements.txt not found")
    
    def check_files_exist(self, file_paths):
        """Check if files exist"""
        return all(os.path.exists(path) for path in file_paths)
    
    def generate_report(self):
        """Generate final status report"""
        print("\n" + "=" * 50)
        print("📋 FEATURE STATUS SUMMARY")
        print("=" * 50)
        
        total_features = len(self.results)
        working_features = sum(1 for result in self.results.values() 
                             if result["status"].startswith("✅"))
        partial_features = sum(1 for result in self.results.values() 
                             if result["status"].startswith("⚠️"))
        broken_features = sum(1 for result in self.results.values() 
                            if result["status"].startswith("❌"))
        
        print(f"Total Features Checked: {total_features}")
        print(f"✅ Working: {working_features}")
        print(f"⚠️ Partial/Warning: {partial_features}")
        print(f"❌ Broken/Missing: {broken_features}")
        
        if self.errors:
            print(f"\n🚨 Critical Issues ({len(self.errors)}):")
            for error in self.errors:
                print(f"  • {error}")
        
        print(f"\n🎯 Overall Status: ", end="")
        if broken_features == 0 and len(self.errors) == 0:
            print("🟢 ALL SYSTEMS OPERATIONAL")
        elif broken_features <= 2:
            print("🟡 MOSTLY OPERATIONAL (Minor Issues)")
        else:
            print("🔴 NEEDS ATTENTION (Major Issues)")
        
        print("\n💡 Next Steps:")
        if not os.path.exists(".env"):
            print("  1. Copy .env.example to .env and configure API keys")
        if "database" in [e.split(":")[0] for e in self.errors]:
            print("  2. Set up PostgreSQL database and update DATABASE_URL")
        if "redis" in [e.split(":")[0] for e in self.errors]:
            print("  3. Start Redis server (docker-compose up redis)")
        if settings.GEMINI_API_KEY == "":
            print("  4. Configure GEMINI_API_KEY for full AI functionality")
        
        print("  5. Run 'make dev' to set up development environment")
        print("  6. Run 'make api' to start the backend server")
        print("  7. Run 'make streamlit' to start the frontend")


async def main():
    """Main function"""
    checker = FeatureChecker()
    await checker.check_all_features()


if __name__ == "__main__":
    asyncio.run(main())
